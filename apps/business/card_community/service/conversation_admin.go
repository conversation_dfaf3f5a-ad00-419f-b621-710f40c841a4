package service

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetConversationListForAdmin 获取管理端会话列表
func (s *Service) GetConversationListForAdmin(req *define.GetConversationAdminListReq) (*define.GetConversationAdminListResp, error) {
	conversationSchema := repo.GetQuery().Conversation
	queryBuilder := search.NewQueryBuilder()

	// 根据日期类型和日期范围筛选
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		// 根据日期类型选择筛选字段
		if req.DateType == enums.DateTypeCreateTime {
			queryBuilder = queryBuilder.Gte(conversationSchema.CreatedAt, req.StartTime)
			queryBuilder = queryBuilder.Lte(conversationSchema.CreatedAt, req.EndTime)
		} else if req.DateType == enums.DateTypeLastMessageTime {
			queryBuilder = queryBuilder.Gte(conversationSchema.LastMessageTime, req.StartTime)
			queryBuilder = queryBuilder.Lte(conversationSchema.LastMessageTime, req.EndTime)
		}
	}

	// 根据会话ID筛选
	if req.ID != "" {
		queryBuilder = queryBuilder.Eq(conversationSchema.ID, req.ID)
	}

	// 根据帖子ID筛选（通过消息表关联查询参与者）
	if req.PostID != "" {
		participantIDs, err := logic.GetParticipantIDsByPostID(s.ctx, req.PostID)
		if err != nil {
			return nil, err
		}

		if len(participantIDs) == 0 {
			// 没有找到相关参与者，返回空列表
			return &define.GetConversationAdminListResp{
				List:  []*define.GetConversationAdminListData{},
				Total: 0,
			}, nil
		}

		// 直接在主查询中添加参与者ID筛选条件
		queryBuilder = queryBuilder.In(conversationSchema.ParticipantID, participantIDs)
	}

	// 根据商家用户ID筛选
	if req.MerchantUserID != "" {
		queryBuilder = queryBuilder.Eq(conversationSchema.ParticipantID, req.MerchantUserID)
	}

	// 根据客户用户ID筛选（查询对方参与者ID）
	if req.ClientUserID != "" {
		queryBuilder = queryBuilder.Eq(conversationSchema.OtherParticipantID, req.ClientUserID)
	}

	// 根据关键字搜索消息内容
	if req.Keyword != "" {
		userIDs, err := logic.GetUserIDsByKeyword(s.ctx, req.Keyword)
		if err != nil {
			return nil, err
		}

		if len(userIDs) == 0 {
			// 没有找到匹配的用户，返回空列表
			return &define.GetConversationAdminListResp{
				List:  []*define.GetConversationAdminListData{},
				Total: 0,
			}, nil
		}

		// 构建用户ID筛选条件（由于双向记录设计，搜索参与者ID或对方ID）
		orBuilder := queryBuilder.Or()
		for _, userID := range userIDs {
			orBuilder = orBuilder.Eq(conversationSchema.ParticipantID, userID).
				Eq(conversationSchema.OtherParticipantID, userID)
		}
		queryBuilder = orBuilder.Done()
	}

	// 只查询未删除的会话
	queryBuilder = queryBuilder.Eq(conversationSchema.IsDeleted, int32(0))

	// 只查询商家类型的会话记录
	queryBuilder = queryBuilder.Eq(conversationSchema.ParticipantType, int32(enums.ParticipantTypeMerchant))

	// 按最近消息时间倒序排列
	queryBuilder = queryBuilder.OrderByDesc(conversationSchema.LastMessageTime).
		OrderByDesc(conversationSchema.CreatedAt)

	queryWrapper := queryBuilder.Build()

	// 分页查询
	conversations, total, err := repo.NewConversationRepo(conversationSchema.WithContext(s.ctx)).SelectPage(
		queryWrapper,
		req.GetPage(),
		req.GetPageSize(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询会话列表失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	// 如果没有会话，直接返回空列表
	if len(conversations) == 0 {
		return &define.GetConversationAdminListResp{
			List:  []*define.GetConversationAdminListData{},
			Total: 0,
		}, nil
	}

	// 收集所有用户ID
	userIDs := make([]string, 0, len(conversations)*2)
	for _, conv := range conversations {
		userIDs = append(userIDs, conv.ParticipantID, conv.OtherParticipantID)
	}

	// 批量获取用户信息
	userMap, err := facade.GetNodeUserMap(s.ctx, userIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		return nil, define.CC500105Err
	}

	// 统计每个会话的消息数量
	messageCountMap, err := logic.GetConversationMessageCounts(s.ctx, conversations)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量统计会话消息数量失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	// 构建响应数据
	list := make([]*define.GetConversationAdminListData, 0, len(conversations))
	for _, conv := range conversations {
		// 确定客户和商家信息
		var clientUserInfo, merchantUserInfo define.UserInfo
		// 参与者是商家，对方是用户
		merchantUser := userMap[conv.ParticipantID]
		merchantUserInfo = define.UserInfo{
			ID:     conv.ParticipantID,
			Name:   merchantUser.Nickname,
			Avatar: merchantUser.Avatar,
		}

		clientUser := userMap[conv.OtherParticipantID]
		clientUserInfo = define.UserInfo{
			ID:     conv.OtherParticipantID,
			Name:   clientUser.Nickname,
			Avatar: clientUser.Avatar,
		}

		list = append(list, &define.GetConversationAdminListData{
			ID:               conv.ID,
			ClientUserInfo:   clientUserInfo,
			MerchantUserInfo: merchantUserInfo,
			MessageCount:     messageCountMap[conv.ID],
			LastMessageTime:  conv.LastMessageTime,
			CreatedAt:        conv.CreatedAt,
		})
	}

	return &define.GetConversationAdminListResp{
		List:  list,
		Total: total,
	}, nil
}

// GetConversationDetailForAdmin 获取管理端会话详情
func (s *Service) GetConversationDetailForAdmin(req *define.GetConversationAdminDetailReq) (*define.GetConversationAdminDetailResp, error) {
	// 查询会话记录
	conversationSchema := repo.GetQuery().Conversation
	queryWrapper := search.NewQueryBuilder().
		Eq(conversationSchema.ID, req.ID).
		Eq(conversationSchema.IsDeleted, int32(0)).                                   // 只查询未删除的会话
		Eq(conversationSchema.ParticipantType, int32(enums.ParticipantTypeMerchant)). // 只查询商家类型的会话记录
		Build()

	conversation, err := repo.NewConversationRepo(conversationSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Ctx(s.ctx).Warnf("会话记录不存在，会话ID：%s", req.ID)
			return nil, define.CC500101Err.SetMsg("会话记录不存在")
		}
		log.Ctx(s.ctx).Errorf("查询会话记录失败，会话ID：%s，错误：%v", req.ID, err)
		return nil, commondefine.CommonErr.Err(err)
	}

	// 收集用户ID
	userIDs := []string{conversation.ParticipantID, conversation.OtherParticipantID}

	// 批量获取用户信息
	userMap, err := facade.GetNodeUserMap(s.ctx, userIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		return nil, define.CC500105Err
	}

	// 确定客户和商家信息（参与者是商家，对方是用户）
	merchantUser := userMap[conversation.ParticipantID]
	merchantUserInfo := define.UserInfo{
		ID:     conversation.ParticipantID,
		Name:   merchantUser.Nickname,
		Avatar: merchantUser.Avatar,
	}

	clientUser := userMap[conversation.OtherParticipantID]
	clientUserInfo := define.UserInfo{
		ID:     conversation.OtherParticipantID,
		Name:   clientUser.Nickname,
		Avatar: clientUser.Avatar,
	}

	return &define.GetConversationAdminDetailResp{
		ID:               conversation.ID,
		ClientUserInfo:   clientUserInfo,
		MerchantUserInfo: merchantUserInfo,
		CreatedAt:        conversation.CreatedAt,
		UpdatedAt:        conversation.UpdatedAt,
	}, nil
}
